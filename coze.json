{"nodes": [{"blocks": [], "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"defaultValue": "<PERSON><PERSON><PERSON>", "description": "本次请求绑定的会话，会自动写入消息、会从该会话读对话历史。", "name": "CONVERSATION_NAME", "required": false, "type": "string"}, {"description": "用户本轮对话输入内容", "name": "BOT_USER_INPUT", "required": false, "type": "string"}, {"name": "query", "required": true, "type": "string"}], "settings": null, "trigger_parameters": [{"description": "本次请求绑定的会话，会自动写入消息、会从该会话读对话历史。", "name": "CONVERSATION_NAME", "required": false, "type": "string"}, {"description": "用户本轮对话输入内容", "name": "BOT_USER_INPUT", "required": false, "type": "string"}, {"name": "query", "required": true, "type": "string"}], "version": ""}, "edges": null, "id": "100001", "meta": {"position": {"x": 0, "y": 0}}, "type": "1"}, {"blocks": [], "data": {"inputs": {"content": {"type": "string", "value": {"content": "{{output}}", "type": "literal"}}, "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "189445", "name": "output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "output"}], "streamingOutput": true, "terminatePlan": "useAnswerContent"}, "nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}}, "edges": null, "id": "900001", "meta": {"position": {"x": 1036.3872700614127, "y": 11.92061926145577}}, "type": "2"}, {"blocks": [], "data": {"inputs": {"fcParamVar": {"knowledgeFCParam": {}}, "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "query", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "query"}], "llmParam": [{"input": {"type": "integer", "value": {"content": "1706077826", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "豆包·Function call模型", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "modleName"}, {"input": {"type": "string", "value": {"content": "balance", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "float", "value": {"content": "1", "rawMeta": {"type": 4}, "type": "literal"}}, "name": "temperature"}, {"input": {"type": "float", "value": {"content": "0.7", "rawMeta": {"type": 4}, "type": "literal"}}, "name": "topP"}, {"input": {"type": "integer", "value": {"content": "2", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "integer", "value": {"content": "1024", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "string", "value": {"content": "{{query}}", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "rawMeta": {"type": 3}, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "systemPrompt"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "stableSystemPrompt"}, {"input": {"type": "boolean", "value": {"content": false, "rawMeta": {"type": 3}, "type": "literal"}}, "name": "canContinue"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "loopPromptVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "loopPromptName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "loopPromptId"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 600000}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "subTitle": "大模型", "title": "大模型"}, "outputs": [{"name": "output", "type": "string"}], "version": "3"}, "edges": null, "id": "189445", "meta": {"position": {"x": 532.7283606050815, "y": 0}}, "type": "3"}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "189445", "sourcePortID": ""}, {"sourceNodeID": "189445", "targetNodeID": "900001", "sourcePortID": ""}], "versions": {"loop": "v2"}}