import { BaseEdge, BaseNode, Variable, WorkflowData } from "@/types/workflow";

export interface WorkflowStore {
  // 状态
  currentWorkflow: WorkflowData | null;
  selectedNodeId: string | null;
  isDebugMode: boolean;
  isDirty: boolean;
  // ...其他状态

  // 操作方法
  setCurrentWorkflow: (workflow: WorkflowData) => void;
  setSelectedNodeId: (id: string | null) => void;
  setIsDebugMode: (isDebugMode: boolean) => void;
  setIsDirty: (isDirty: boolean) => void;
  updateNode: (nodeId: string, updates: Partial<BaseNode>) => void;
  addNode: (node: Omit<BaseNode, "id">) => void;
  deleteNode: (nodeId: string) => void;
  addEdge: (edge: Omit<BaseEdge, "id">) => void;
  deleteEdge: (edgeId: string) => void;
  getAvailableVariables: (nodeId: string) => Variable[];
  // ...其他操作方法
}
