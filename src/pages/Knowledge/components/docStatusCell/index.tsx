import { Badge } from "@/components/main/badge";
import { RestoreIcon } from "@/components/main/icon";
import Popconfirm from "@/components/main/popconfirm";
import { RunningStatus, runningStatusMap } from "@/constants/knowledge";
import type { DocumentItem } from "@/types/knowledge";

type DocStatusCellProps = {
  record: DocumentItem;
  onRun?: (id: string) => void;
};

export default function DocStatusCell({ record, onRun }: DocStatusCellProps) {
  const status = runningStatusMap[record.run] || {};

  return (
    <div className="flex items-center gap-2">
      {record.run === RunningStatus.FAIL ? (
        <Popconfirm
          title={status.text}
          content={record.progress_msg}
          placement="left"
          type="error"
          trigger="hover"
          footer={null}
        >
          <Badge variant={status.variant} showDot>
            {status.text}
          </Badge>
        </Popconfirm>
      ) : (
        <Badge variant={status.variant} showDot>
          <span>{status.text}</span>
          {record.run === RunningStatus.INDEXING && (
            <span className="ml-1">
              {((record.progress || 0) * 100).toFixed(2)}%
            </span>
          )}
        </Badge>
      )}
      <Popconfirm
        title="确认重新索引该文档吗？"
        placement="top"
        type="info"
        confirmText="重新索引"
        onConfirm={() => {
          onRun?.(record.id);
        }}
      >
        <RestoreIcon className="text-base text-primary-default" />
      </Popconfirm>
    </div>
  );
}
