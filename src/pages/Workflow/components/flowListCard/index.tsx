import ForwardedIconComponent from "@/components/common/genericIconComponent";
import { Button } from "@/components/main/button";
import { ClockIcon, EditIcon, EllipsisIcon } from "@/components/main/icon";
import ListCard from "@/components/main/listCard";
import { useCustomNavigate } from "@/customization/hooks/use-custom-navigate";
import { useGetFlowIcon } from "@/hooks/workflow/useGetFlowIcon";
import useFlowsManagerStore from "@/stores/flowsManagerStore";
import { FlowType } from "@/types/flow";
import { Dropdown } from "antd";

type FlowListCardProps = {
  flowData: FlowType;
  onDetele: (id: string) => void;
  onEdit: (id: string) => void;
};

export default function FlowListCard({
  flowData,
  onDetele,
  onEdit,
}: FlowListCardProps) {
  const { icon, iconColor } = useGetFlowIcon({ flowData });

  const navigate = useCustomNavigate();
  const setFlowToCanvas = useFlowsManagerStore(
    (state) => state.setFlowToCanvas,
  );

  const handleClick = async () => {
    await setFlowToCanvas(flowData);
    navigate(`/workflow/${flowData.id}/config`);
  };

  const renderIcon = () => {
    return (
      <ForwardedIconComponent
        name={flowData?.icon || icon}
        aria-hidden="true"
        className="flex h-5 w-5 items-center justify-center"
      />
    );
  };

  const renderFooter = () => {
    return (
      <div className="flex gap-2" onClick={(e) => e.stopPropagation()}>
        <Button
          variant="outline"
          className="w-full !text-primary-default"
          size="sm"
          onClick={(e) => {
            onEdit(flowData.id);
          }}
        >
          <EditIcon className="text-base" />
          编辑工作流
        </Button>
        <Dropdown
          menu={{
            items: [{ key: "delete", label: "删除" }],
            onClick: () => onDetele(flowData.id),
          }}
          getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
        >
          <Button variant="outline" size="iconSm">
            <EllipsisIcon className="text-base text-text-2" />
          </Button>
        </Dropdown>
      </div>
    );
  };

  const renderDescription = () => {
    return (
      <>
        <ClockIcon className="mr-1 text-base text-text-4" />
        <div className="line-clamp-1 break-all">{flowData.updated_at}</div>
      </>
    );
  };

  return (
    <ListCard
      title={flowData.name}
      content={flowData.description}
      iconWrapperClass={iconColor}
      onClick={handleClick}
      renderIcon={renderIcon}
      renderFooter={renderFooter}
      renderDescription={renderDescription}
    />
  );
}
