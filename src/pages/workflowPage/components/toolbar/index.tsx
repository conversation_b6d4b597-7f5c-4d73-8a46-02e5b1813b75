import { ZoomInOutlined, ZoomOutOutlined } from "@ant-design/icons";
import { Panel } from "@xyflow/react";
import { Button, Dropdown } from "antd";
import { useMemo } from "react";

import { SquarePlusIcon } from "@/components/main/icon";
import { BASE_NODES } from "@/constants/workflow";
import { useWorkflowStore } from "@/stores/workflowStore";

export default function Toolbar() {
  const debugPanel = useWorkflowStore((state) => state.debugPanel);
  const configPanel = useWorkflowStore((state) => state.configPanel);

  // 计算工具栏的左偏移量，为打开的面板留出空间
  const leftOffset = useMemo(() => {
    let offset = 0;

    if (debugPanel.isOpen) {
      offset += (debugPanel.width + 8) / 2; // 面板宽度的一半 + 间距的一半
    }
    if (configPanel.isOpen) {
      offset += (configPanel.width + 8) / 2; // 面板宽度的一半 + 间距的一半
    }

    return offset;
  }, [debugPanel.isOpen, debugPanel.width, configPanel.isOpen, configPanel.width]);

  const renderMenu = () => {
    return (
      <div className="flex w-[200px] flex-col gap-2 rounded-lg border border-border-1 bg-white p-4 shadow-lg">
        {BASE_NODES.map((node) => (
          <div
            key={node.type}
            className="flex h-[48px] cursor-pointer items-center justify-center rounded-md border border-border-1 p-2"
          >
            {node.label}
          </div>
        ))}
      </div>
    );
  };

  return (
    <Panel
      position="bottom-center"
      className="flex items-center rounded-md border border-border-1 bg-white p-2 shadow !z-20"
      style={{
        transform: `translateX(-${leftOffset}px) translateX(68px) translateX(-15px) translateX(-50%)`,
      }}
    >
      <div className="flex items-center gap-2">
        <ZoomInOutlined className="text-text-2" />
        <span className="text-xs">100%</span>
        <ZoomOutOutlined className="text-text-2" />
      </div>
      <div className="mx-2 h-5 border-r-[1px] border-border-1"></div>
      <Dropdown popupRender={renderMenu} placement="top" trigger={["click"]}>
        <Button type="primary" size="small" className="text-xs">
          <SquarePlusIcon />
          添加节点
        </Button>
      </Dropdown>
    </Panel>
  );
}
