import { ZoomInOutlined, ZoomOutOutlined } from "@ant-design/icons";
import { Panel } from "@xyflow/react";
import { Button, Dropdown } from "antd";

import { SquarePlusIcon } from "@/components/main/icon";
import { BASE_NODES } from "@/constants/workflow";

export default function Toolbar() {
  const renderMenu = () => {
    return (
      <div className="flex w-[200px] flex-col gap-2 rounded-lg border border-border-1 bg-white p-4 shadow-lg">
        {BASE_NODES.map((node) => (
          <div
            key={node.type}
            className="flex h-[48px] cursor-pointer items-center justify-center rounded-md border border-border-1 p-2"
          >
            {node.label}
          </div>
        ))}
      </div>
    );
  };

  return (
    <Panel
      position="bottom-center"
      className="flex items-center rounded-md border border-border-1 bg-white p-2 shadow"
    >
      <div className="flex items-center gap-2">
        <ZoomInOutlined className="text-text-2" />
        <span className="text-xs">100%</span>
        <ZoomOutOutlined className="text-text-2" />
      </div>
      <div className="mx-2 h-5 border-r-[1px] border-border-1"></div>
      <Dropdown popupRender={renderMenu} placement="top" trigger={["click"]}>
        <Button type="primary" size="small" className="text-xs">
          <SquarePlusIcon />
          添加节点
        </Button>
      </Dropdown>
    </Panel>
  );
}
