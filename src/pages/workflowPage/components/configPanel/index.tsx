import { But<PERSON> } from "antd";

import { CloseIcon } from "@/components/main/icon";
import { useWorkflowStore } from "@/stores/workflowStore";
import { cn } from "@/utils/utils";

export default function ConfigPanel() {
  const setSelectedNodeId = useWorkflowStore(
    (state) => state.setSelectedNodeId,
  );
  const currentWorkflow = useWorkflowStore((state) => state.currentWorkflow);
  const isDebugMode = useWorkflowStore((state) => state.isDebugMode);
  const selectedNodeId = useWorkflowStore((state) => state.selectedNodeId);

  if (!selectedNodeId) return null;

  const node = currentWorkflow?.nodes.find(
    (node) => node.id === selectedNodeId,
  );

  return (
    <div
      className={cn(
        "absolute bottom-2 right-2 top-2 z-10 w-[360px] rounded-md border border-border-1 bg-white p-3",
        isDebugMode ? "right-[376px]" : "right-2",
      )}
    >
      <div className="flex items-center justify-between">
        <div>{node?.label}</div>
        <Button
          type="text"
          size="small"
          onClick={() => setSelectedNodeId(null)}
        >
          <CloseIcon />
        </Button>
      </div>
    </div>
  );
}
