import { Button } from "antd";

import { CloseIcon } from "@/components/main/icon";
import { useWorkflowStore } from "@/stores/workflowStore";
import ResizablePanel from "../resizablePanel";

export default function ConfigPanel() {
  const currentWorkflow = useWorkflowStore((state) => state.currentWorkflow);
  const selectedNodeId = useWorkflowStore((state) => state.selectedNodeId);
  const configPanel = useWorkflowStore((state) => state.configPanel);
  const debugPanel = useWorkflowStore((state) => state.debugPanel);
  const setConfigPanelOpen = useWorkflowStore((state) => state.setConfigPanelOpen);
  const setConfigPanelWidth = useWorkflowStore((state) => state.setConfigPanelWidth);

  if (!configPanel.isOpen || !selectedNodeId) return null;

  const node = currentWorkflow?.nodes.find(
    (node) => node.id === selectedNodeId,
  );

  // 计算右侧偏移量：如果调试面板打开，需要为其留出空间
  const rightOffset = debugPanel.isOpen ? debugPanel.width + 8 : 8;

  return (
    <ResizablePanel
      width={configPanel.width}
      onWidthChange={setConfigPanelWidth}
      position="right"
      rightOffset={rightOffset}
    >
      <div className="flex items-center justify-between">
        <div>{node?.label}</div>
        <Button
          type="text"
          size="small"
          onClick={() => setConfigPanelOpen(false)}
        >
          <CloseIcon />
        </Button>
      </div>
    </ResizablePanel>
  );
}
