import { BASE_NODES } from "@/constants/workflow";

export default function NodeSidebar() {
  return (
    <div className="absolute bottom-0 left-0 top-0 z-10 flex w-[240px] flex-col gap-2 border-r-[1px] border-border-1 bg-white p-4">
      {BASE_NODES.map((node) => (
        <div
          key={node.type}
          className="flex h-[48px] cursor-pointer items-center justify-center rounded-md border border-border-1 p-2"
        >
          {node.label}
        </div>
      ))}
    </div>
  );
}
