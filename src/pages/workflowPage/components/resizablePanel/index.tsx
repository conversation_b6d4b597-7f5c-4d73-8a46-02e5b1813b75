import { cn } from "@/utils/utils";
import React, { useCallback, useRef, useState } from "react";

interface ResizablePanelProps {
  children: React.ReactNode;
  width: number;
  onWidthChange: (width: number) => void;
  minWidth?: number;
  maxWidth?: number;
  className?: string;
  position: "right" | "left";
  rightOffset?: number;
}

export default function ResizablePanel({
  children,
  width,
  onWidthChange,
  minWidth = 360,
  maxWidth = 480,
  className,
  position,
  rightOffset = 0,
}: ResizablePanelProps) {
  const [isDragging, setIsDragging] = useState(false);
  const panelRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      startXRef.current = e.clientX;
      startWidthRef.current = width;

      const handleMouseMove = (e: MouseEvent) => {
        const deltaX =
          position === "right"
            ? startXRef.current - e.clientX // 右侧面板：向左拖动增加宽度
            : e.clientX - startXRef.current; // 左侧面板：向右拖动增加宽度

        const newWidth = Math.max(
          minWidth,
          Math.min(maxWidth, startWidthRef.current + deltaX),
        );

        onWidthChange(newWidth);
      };

      const handleMouseUp = () => {
        setIsDragging(false);
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };

      setIsDragging(true);
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    },
    [width, onWidthChange, minWidth, maxWidth, position],
  );

  const panelStyle = {
    width: `${width}px`,
    right: position === "right" ? `${rightOffset}px` : undefined,
    left: position === "left" ? `${rightOffset}px` : undefined,
  };

  return (
    <div
      ref={panelRef}
      className={cn(
        "absolute bottom-2 top-2 z-10 rounded-md border border-border-1 bg-white",
        className,
      )}
      style={panelStyle}
    >
      {/* 拖拽手柄 */}
      <div
        className={cn(
          "absolute bottom-0 top-0 w-1 cursor-col-resize transition-colors hover:bg-bg-primary-1",
          position === "right" ? "left-0" : "right-0",
          isDragging && "bg-bg-primary-1",
        )}
        onMouseDown={handleMouseDown}
      />

      {/* 面板内容 */}
      <div className="h-full p-3">{children}</div>
    </div>
  );
}
