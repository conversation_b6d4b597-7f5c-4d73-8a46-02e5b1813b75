import {
  Background,
  Edge,
  EdgeChange,
  MiniMap,
  Node,
  NodeChange,
  ReactFlow,
  addEdge,
  applyEdgeChanges,
  applyNodeChanges,
  Connection,
} from "@xyflow/react";
import React, { useCallback, useEffect, useState } from "react";

import { useWorkflowStore } from "@/stores/workflowStore";
import ConfigPanel from "../configPanel";
import DebugPanel from "../debugPanel";
import GenericNode from "../node";
import PanelControls from "../panelControls";
import Toolbar from "../toolbar";

const nodeTypes = {
  genericNode: GenericNode,
};

export default function CanvasContainer() {
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);

  const setSelectedNodeId = useWorkflowStore(
    (state) => state.setSelectedNodeId,
  );
  const currentWorkflow = useWorkflowStore((state) => state.currentWorkflow);

  useEffect(() => {
    if (currentWorkflow) {
      setNodes(
        currentWorkflow.nodes.map((node) => ({
          id: node.id,
          position: node.position,
          data: node.data,
        })),
      );
      setEdges(currentWorkflow.edges);
    }
  }, [currentWorkflow]);

  const onNodesChange = useCallback(
    (changes: NodeChange[]) =>
      setNodes((nodesSnapshot) => applyNodeChanges(changes, nodesSnapshot)),
    [],
  );
  const onEdgesChange = useCallback(
    (changes: EdgeChange[]) =>
      setEdges((edgesSnapshot) => applyEdgeChanges(changes, edgesSnapshot)),
    [],
  );
  const onConnect = useCallback(
    (params: Connection) => setEdges((edgesSnapshot) => addEdge(params, edgesSnapshot)),
    [],
  );

  const onNodeClick = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      setSelectedNodeId(node.id);
    },
    [setSelectedNodeId],
  );

  const onPaneClick = useCallback(() => {
    setSelectedNodeId(null);
  }, [setSelectedNodeId]);

  return (
    <ReactFlow
      className="w-full flex-1"
      proOptions={{ hideAttribution: true }}
      nodeTypes={nodeTypes}
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onConnect={onConnect}
      onNodeClick={onNodeClick}
      onPaneClick={onPaneClick}
      fitView
    >
      <Background size={2} gap={20} bgColor="#f9f9f9" />
      <MiniMap
        position="bottom-left"
        className="rounded-lg border border-border-1 shadow"
        style={{
          width: 120,
          height: 90,
        }}
      />
      <Toolbar />
      <PanelControls />
      <ConfigPanel />
      <DebugPanel />
    </ReactFlow>
  );
}
