import { Button } from "antd";

import { CloseIcon } from "@/components/main/icon";
import { useWorkflowStore } from "@/stores/workflowStore";

export default function DebugPanel() {
  const setIsDebugMode = useWorkflowStore((state) => state.setIsDebugMode);
  const isDebugMode = useWorkflowStore((state) => state.isDebugMode);

  if (!isDebugMode) return null;

  return (
    <div className="absolute bottom-2 right-2 top-2 z-10 w-[360px] rounded-md border border-border-1 bg-white p-3">
      <div className="flex items-center justify-between">
        <div>调试</div>
        <Button type="text" size="small" onClick={() => setIsDebugMode(false)}>
          <CloseIcon />
        </Button>
      </div>
    </div>
  );
}
