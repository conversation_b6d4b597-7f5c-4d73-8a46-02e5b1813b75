import { Button } from "antd";

import { CloseIcon } from "@/components/main/icon";
import { useWorkflowStore } from "@/stores/workflowStore";
import ResizablePanel from "../resizablePanel";

export default function DebugPanel() {
  const debugPanel = useWorkflowStore((state) => state.debugPanel);
  const setDebugPanel = useWorkflowStore((state) => state.setDebugPanel);

  if (!debugPanel.isOpen) return null;

  return (
    <ResizablePanel
      width={debugPanel.width}
      onWidthChange={(width) => setDebugPanel({ width })}
      position="right"
      rightOffset={8}
    >
      <div className="flex items-center justify-between">
        <div>调试</div>
        <Button
          type="text"
          size="small"
          onClick={() => setDebugPanel({ isOpen: false })}
        >
          <CloseIcon />
        </Button>
      </div>
    </ResizablePanel>
  );
}
