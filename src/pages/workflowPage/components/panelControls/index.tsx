import { Panel } from "@xyflow/react";
import { Button, Tooltip } from "antd";
import { BugOutlined, SettingOutlined } from "@ant-design/icons";

import { useWorkflowStore } from "@/stores/workflowStore";

export default function PanelControls() {
  const debugPanel = useWorkflowStore((state) => state.debugPanel);
  const configPanel = useWorkflowStore((state) => state.configPanel);
  const setDebugPanelOpen = useWorkflowStore((state) => state.setDebugPanelOpen);
  const setConfigPanelOpen = useWorkflowStore((state) => state.setConfigPanelOpen);
  const selectedNodeId = useWorkflowStore((state) => state.selectedNodeId);

  return (
    <Panel
      position="top-right"
      className="flex flex-col gap-2"
    >
      <Tooltip title="调试面板" placement="left">
        <Button
          type={debugPanel.isOpen ? "primary" : "default"}
          icon={<BugOutlined />}
          onClick={() => setDebugPanelOpen(!debugPanel.isOpen)}
          size="small"
        />
      </Tooltip>
      
      <Tooltip title="配置面板" placement="left">
        <Button
          type={configPanel.isOpen ? "primary" : "default"}
          icon={<SettingOutlined />}
          onClick={() => setConfigPanelOpen(!configPanel.isOpen)}
          disabled={!selectedNodeId}
          size="small"
        />
      </Tooltip>
    </Panel>
  );
}
