import { create } from "zustand";

import { BaseEdge, BuiltinNode, WorkflowData } from "@/types/workflow";
import { WorkflowStore } from "@/types/zustand/workflow";
import { v4 as uuid } from "uuid";

export const useWorkflowStore = create<WorkflowStore>((set, get) => ({
  currentWorkflow: null,
  selectedNodeId: null,
  isDebugMode: false,
  isDirty: false,
  // 面板状态初始化
  debugPanel: {
    isOpen: false,
    width: 360,
  },
  configPanel: {
    isOpen: false,
    width: 360,
  },

  setCurrentWorkflow: (workflow: WorkflowData) => {
    set({ currentWorkflow: workflow });
  },
  setSelectedNodeId: (id: string | null) => {
    set({ selectedNodeId: id });
    // 当选择节点时自动打开配置面板
    if (id) {
      set((state) => ({
        configPanel: { ...state.configPanel, isOpen: true },
      }));
    } else {
      set((state) => ({
        configPanel: { ...state.configPanel, isOpen: false },
      }));
    }
  },
  setIsDebugMode: (isDebugMode: boolean) => {
    set((state) => ({
      isDebugMode,
      debugPanel: { ...state.debugPanel, isOpen: isDebugMode },
    }));
  },
  setIsDirty: (isDirty: boolean) => {
    set({ isDirty });
  },
  // 面板操作方法
  setDebugPanelOpen: (isOpen: boolean) => {
    set((state) => ({
      debugPanel: { ...state.debugPanel, isOpen },
      isDebugMode: isOpen,
    }));
  },
  setDebugPanelWidth: (width: number) => {
    const clampedWidth = Math.max(360, Math.min(480, width));
    set((state) => ({
      debugPanel: { ...state.debugPanel, width: clampedWidth },
    }));
  },
  setConfigPanelOpen: (isOpen: boolean) => {
    set((state) => ({
      configPanel: { ...state.configPanel, isOpen },
    }));
    // 如果关闭配置面板，同时取消节点选择
    if (!isOpen) {
      set({ selectedNodeId: null });
    }
  },
  setConfigPanelWidth: (width: number) => {
    const clampedWidth = Math.max(360, Math.min(480, width));
    set((state) => ({
      configPanel: { ...state.configPanel, width: clampedWidth },
    }));
  },
  updateNode: (nodeId: string, updates: Partial<BuiltinNode>) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedNodes = currentWorkflow.nodes.map((node) =>
      node.id === nodeId ? { ...node, ...updates } : node,
    );

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
      },
    });
  },
  addNode: (node: Omit<BuiltinNode, "id">) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedNodes = [...currentWorkflow.nodes, { ...node, id: uuid() }];

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
      },
    });
  },
  deleteNode: (nodeId: string) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedNodes = currentWorkflow.nodes.filter(
      (node) => node.id !== nodeId,
    );

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
      },
    });
  },
  addEdge: (edge: Omit<BaseEdge, "id">) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedEdges = [...currentWorkflow.edges, { ...edge, id: uuid() }];

    set({
      currentWorkflow: {
        ...currentWorkflow,
        edges: updatedEdges,
      },
    });
  },
  deleteEdge: (edgeId: string) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedEdges = currentWorkflow.edges.filter(
      (edge) => edge.id !== edgeId,
    );

    set({
      currentWorkflow: {
        ...currentWorkflow,
        edges: updatedEdges,
      },
    });
  },
  getAvailableVariables: (nodeId: string) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return [];

    const node = currentWorkflow.nodes.find((node) => node.id === nodeId);
    if (!node) return [];

    const variables = [];

    return variables;
  },
}));
