import { <PERSON><PERSON>, Divider, Select } from "antd";
import { useContext, useEffect, useMemo } from "react";
import { useLocation, useParams } from "react-router-dom";

import aiWorksLogo from "@/assets/AIWorksLogo.png";
import aiWorksLogoWithText from "@/assets/AIWorksLogoWithText.png";
import {
  AppIcon,
  ControlSquareIcon,
  DocIcon,
  GlobalIcon,
  KnowledgeIcon,
  LeftArrowIcon,
  LogoutIcon,
  ModelIcon,
  RightArrowIcon,
  SettingIcon,
  SidebarTriggerIcon,
  TargetIcon,
  WorkflowIcon,
} from "@/components/main/icon";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { ACCESS_TOKEN } from "@/constants/constants";
import { AuthContext } from "@/contexts/authContext";
import { useLogout } from "@/controllers/API/queries/auth";
import { useSwitchTeam } from "@/controllers/API/queries/team/useSwitchTeam";
import { useCustomNavigate } from "@/customization/hooks/use-custom-navigate";
import { useModal } from "@/hooks/useModal";
import SettingModal from "@/modals/settingModal";
import useAuthStore from "@/stores/authStore";
import { cn } from "@/utils/utils";

interface MenuItem {
  title: string;
  key: string;
  url?: string;
  icon?: React.ReactNode;
}

interface TeamOption {
  value: string;
  label: JSX.Element;
}

enum MenuType {
  App = "app",
  Workflow = "workflow",
  Knowledge = "knowledge",
  Model = "model",
}

const team = {
  title: "团队切换",
  key: "currentTeam",
};

const app: MenuItem = {
  title: "应用管理",
  key: "app",
  url: "/app",
  icon: <AppIcon />,
};
const workflow: MenuItem = {
  title: "工作流",
  key: "workflow",
  url: "/workflow",
  icon: <WorkflowIcon />,
};
const knowledge: MenuItem = {
  title: "知识库",
  key: "knowledge",
  url: "/knowledge",
  icon: <KnowledgeIcon />,
};
const model: MenuItem = {
  title: "模型供应商",
  key: "model",
  url: "/model",
  icon: <ModelIcon />,
};
const rootMenuItems: MenuItem[] = [team, app, workflow, knowledge, model];

export default function Sider() {
  const { open } = useSidebar();
  const navigate = useCustomNavigate();
  const location = useLocation();
  const { knowledgeId, appId, workflowId } = useParams();

  const setCurrentTeamId = useAuthStore((state) => state.setCurrentTeamId);
  const teams = useAuthStore((state) => state.userData?.tenants || []);
  const currentTeamId = useAuthStore((state) => state.currentTeamId);

  const teamOptions: TeamOption[] = teams.map((item) => {
    return {
      value: item.tenant_id,
      label: (
        <div className="jusitfy-center flex items-center gap-1">
          <Avatar
            shape="square"
            className="text-m border-[1px] border-border-1 bg-bg-green-1 font-medium text-primary-default"
            size={20}
          >
            {item.name.slice(0, 1)}
          </Avatar>
          <span>{item.name}</span>
        </div>
      ),
    };
  });

  const { mutate } = useSwitchTeam();

  const subMenuType: MenuType | undefined = useMemo(() => {
    if (appId) return MenuType.App;
    if (workflowId) return MenuType.Workflow;
    if (knowledgeId) return MenuType.Knowledge;
    return undefined;
  }, [knowledgeId, appId, workflowId]);

  const { userData } = useContext(AuthContext);
  const { mutate: mutationLogout } = useLogout();
  const userEditModal = useModal();

  // dynamically display menu types
  const menuItems = useMemo(() => {
    switch (subMenuType) {
      case MenuType.App: {
        return [
          {
            title: "编排",
            url: `/app/${appId}/config`,
            icon: <ControlSquareIcon />,
          },
          {
            title: "发布",
            url: `/app/${appId}/publish`,
            icon: <GlobalIcon />,
          },
        ];
      }
      case MenuType.Workflow: {
        return [
          {
            title: "编排",
            url: `/workflow/${workflowId}/config`,
            icon: <ControlSquareIcon />,
          },
          {
            title: "发布",
            url: `/workflow/${workflowId}/publish`,
            icon: <GlobalIcon />,
          },
        ];
      }
      case MenuType.Knowledge: {
        return [
          {
            title: "文件列表",
            url: `/knowledge/${knowledgeId}/document`,
            icon: <DocIcon />,
          },
          {
            title: "召回测试",
            url: `/knowledge/${knowledgeId}/testing`,
            icon: <TargetIcon />,
          },
          {
            title: "配置",
            url: `/knowledge/${knowledgeId}/setting`,
            icon: <SettingIcon />,
          },
        ];
      }
      default: {
        return rootMenuItems;
      }
    }
  }, [knowledgeId, appId, subMenuType, workflowId]);

  const renderBackItem = () => {
    let rootMenu: MenuItem;
    switch (subMenuType) {
      case MenuType.App: {
        rootMenu = app;
        break;
      }
      case MenuType.Workflow: {
        rootMenu = workflow;
        break;
      }
      case MenuType.Knowledge: {
        rootMenu = knowledge;
        break;
      }
      default: {
        return null;
      }
    }
    return (
      <>
        <SidebarMenuItem key="/knowledge">
          <SidebarMenuButton
            className="group text-text-2 data-[active=true]:bg-gradient-menu data-[active=true]:text-primary-default hover:data-[active=true]:bg-transparent group-data-[collapsible=icon]:ml-[6px] group-data-[collapsible=icon]:!px-[6px] group-data-[collapsible=icon]:!py-2 [&:not([data-active='true'])]:hover:bg-bg-light-1"
            tooltip={rootMenu.title}
            onClick={() => navigate(rootMenu?.url ?? "")}
          >
            {open ? <LeftArrowIcon className="!h-4 !w-4" /> : rootMenu.icon}
            <span className="text-text-1 group-data-[active=true]:text-primary-default">
              {rootMenu.title}
            </span>
          </SidebarMenuButton>
        </SidebarMenuItem>
        <SidebarSeparator className={cn(open ? "mx-0" : "")} />
      </>
    );
  };

  const openUserEditModal = () => {
    userEditModal.open();
  };

  const handleLogout = () => {
    mutationLogout();
  };

  const handleTeamChange = (value: string) => {
    mutate(
      { tenant_id: value },
      {
        onSuccess: (data) => {
          localStorage.setItem(ACCESS_TOKEN, data.access_token);
          setCurrentTeamId(value);
        },
      },
    );
  };

  return (
    <Sidebar
      collapsible="icon"
      className="rounded-xl border border-border-1 bg-bg-light-3 p-2 *:bg-bg-light-3 *:!shadow-none"
    >
      <SidebarHeader className="group-data-[collapsible=icon]:px-3.5 group-data-[collapsible=icon]:py-2">
        <SidebarMenu>
          <SidebarMenuItem key="AIWorks-header">
            <SidebarMenuButton
              size="lg"
              className="justify-between pl-0 hover:bg-transparent"
            >
              {open ? (
                <>
                  <img
                    src={aiWorksLogoWithText}
                    alt="AIWorks Logo"
                    className="h-[36px]"
                    onClick={() => navigate("/")}
                  />
                  <SidebarTrigger className="hover:bg-transparent">
                    <SidebarTriggerIcon
                      className="text-2xl text-text-2"
                      svgStyle={{ width: 24, height: 24 }}
                    />
                  </SidebarTrigger>
                </>
              ) : (
                <img
                  src={aiWorksLogo}
                  alt="AIWorks Logo"
                  className="h-full w-full"
                  onClick={() => navigate("/")}
                />
              )}
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="gap-2">
              {!open && (
                <SidebarMenuItem
                  key="sidebar-trigger"
                  className="sidebar-trigger-item"
                >
                  <SidebarMenuButton
                    size="lg"
                    className="ml-[6px] hover:bg-bg-light-1"
                  >
                    <SidebarTrigger className="pl-[7px] hover:bg-bg-light-1">
                      <SidebarTriggerIcon
                        className="text-2xl text-text-2"
                        svgStyle={{ width: 24, height: 24 }}
                      />
                    </SidebarTrigger>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              )}
              {renderBackItem()}
              {menuItems.map((item) => {
                if (item.key === team.key) {
                  if (!open) {
                    return;
                  }
                  return (
                    <div
                      key={item.key}
                      className="flex flex-col gap-1 pb-4 pt-6"
                    >
                      <span className="text-text-3">{item.title}</span>
                      <Select
                        value={currentTeamId}
                        onChange={handleTeamChange}
                        options={teamOptions}
                      />
                    </div>
                  );
                }
                return (
                  <SidebarMenuItem key={item.key}>
                    <SidebarMenuButton
                      className="group text-text-3 hover:text-text-3 data-[active=true]:bg-gradient-menu data-[active=true]:text-primary-default hover:data-[active=true]:bg-transparent group-data-[collapsible=icon]:ml-[6px] group-data-[collapsible=icon]:!px-[6px] group-data-[collapsible=icon]:!py-2 [&:not([data-active='true'])]:hover:bg-bg-light-1"
                      tooltip={item.title}
                      onClick={() => navigate(item.url)}
                      isActive={location.pathname.startsWith(item.url)}
                    >
                      {item.icon}
                      <span className="text-text-1 group-data-[active=true]:text-primary-default">
                        {item.title}
                      </span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className="border-t border-border-1 pt-4">
        <Popover>
          <PopoverTrigger asChild>
            <div className="flex cursor-pointer items-center gap-1 rounded-md px-2 py-[5px] hover:bg-bg-light-1">
              <Avatar
                className="flex h-[20px] w-[20px] items-center justify-center rounded-[20px] border border-border-1 bg-bg-green-1 text-[10px] text-primary-default"
                style={{ fontSize: 14 }}
                size={20}
                src={userData?.avatar}
              >
                {userData?.nickname?.slice(0, 1)?.toUpperCase()}
              </Avatar>
              {open && (
                <>
                  <span className="ml-2 text-[14px] text-text-1">
                    {userData?.nickname}
                  </span>
                  <span className="absolute right-6 float-right text-lg text-text-2">
                    <RightArrowIcon />
                  </span>
                </>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent
            className="flex w-32 flex-col gap-1 rounded-lg bg-bg-light-1 px-2 py-1 shadow-node-message"
            side="right"
            align="end"
            alignOffset={-18}
          >
            <div
              className="flex cursor-pointer items-center gap-2 bg-bg-light-1 px-2 py-[5px] text-text-1 hover:bg-bg-light-3"
              onClick={openUserEditModal}
            >
              <SettingIcon className="text-xl text-text-3" />
              <span className="text-sm text-text-1 group-data-[active=true]:text-primary-default">
                设置
              </span>
            </div>
            <Divider className="my-0.5" />
            <div
              className="flex cursor-pointer items-center gap-2 bg-bg-light-1 px-2 py-[5px] text-text-1 hover:bg-bg-light-3"
              onClick={handleLogout}
            >
              <LogoutIcon className="text-xl text-text-3" />
              <span className="text-sm text-text-1 group-data-[active=true]:text-primary-default">
                退出登录
              </span>
            </div>
          </PopoverContent>
        </Popover>
      </SidebarFooter>

      <SettingModal
        open={userEditModal.visible}
        setOpen={(open) => userEditModal.toggle(open)}
      />
    </Sidebar>
  );
}
